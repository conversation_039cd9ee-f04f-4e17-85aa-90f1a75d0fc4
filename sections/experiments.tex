% ===================================================================
% 实验设置与分析 (Experimental Setup and Analysis)
% ===================================================================

\section{实验设置与分析}

% ===================================================================
% 实验设置 (Experimental Setup)
% ===================================================================


\begin{frame}{实验设置}
	% 上半部分: 关键问题
	\textbf{关键问题}
	\begin{itemize}
		\item \textbf{RQ1: }AgentVerse框架在通用理解与推理任务(如对话回复、创造性写作、数学推理、逻辑推理)中的性能是否优于是否优于单智能体?
		\item \textbf{RQ2: }AgentVerse框架在编码任务(如Humaneval代码补全)中的性能是否优于是否优于单智能体方法?
		\item \textbf{RQ3: }AgentVerse框架协调的多智能体在需要多种工具交互的复杂任务中，性能是否优于独立的ReAct智能体?
		\item \textbf{RQ4: }在具身场景(如Minecraft)下，AgentVerse框架下的多智能体协作是否会涌现特定社会行为?
	\end{itemize}

	\vspace{0.1em} % 增加垂直间距以分隔两部分

	% 下半部分: 实验设置
	\textbf{实验设置}
	\begin{itemize}
		\item \textbf{语言模型}: GPT-3.5-Turbo-0613、GPT-4-0613、GPT-4-0314(用于 Minecraft)
		\item \textbf{集成工具}: Bing Search API、Web Browser、Code Interpreter、Weather API、Billboard API
		\item \textbf{数据集}: 通用理解能力 (FED、Commongen-Challenge)、通用推理能力 (MGSM、Logic Grid Puzzles)、代码能力 (Humaneval )、工具使用 (10个自定义复杂指令)
		\item \textbf{Baseline}: CoT、Solo、ReAct Agent
	\end{itemize}
\end{frame}


\begin{frame}{RQ1:AgentVerse框架在通用理解与推理任务中的性能是否优于单智能体?}
	\begin{center}
		\includegraphics[width=0.9\linewidth]{pic/RQ1.png}
	\end{center}

	{\scriptsize
		\begin{itemize}
			\item CoT: 带CoT（思维链）的单智能体；
			\item Solo:  在决策阶段使用一个智能体的AgentVerse。与CoT相比，Solo额外结合了专家招募、动作执行和评估模块；
			\item Group: 在决策过程中使用多个智能体协作实现AgentVerse。
		\end{itemize}
	}
	AgentVerse框架在通用理解与推理任务中的性能优于单智能体方法。



\end{frame}


\begin{frame}{RQ2:AgentVerse框架在编码任务中的性能是否优于是否优于单智能体?}
	\begin{table}
		\vspace{-0.5em}
		\begin{tabular}{l c c}
			\toprule
			\textbf{Setting} & \textbf{GPT-3.5-Turbo} & \textbf{GPT-4} \\
			\midrule
			CoT & 73.8 & 83.5 \\
			Solo & 74.4 & 87.2 \\
			Group & \textbf{75.6} & \textbf{89.0} \\
			\bottomrule
		\end{tabular}
		\vspace{-1em}
	\end{table}

\end{frame}



\begin{frame}{RQ3:AgentVerse框架在需要多种工具交互的复杂任务中，其性能是否优于独立的ReAct智能体?}

\end{frame}



\begin{frame}{RQ4:在具身场景下，AgentVerse框架下的多智能体协作是否会涌现特定社会行为?}

\end{frame}


