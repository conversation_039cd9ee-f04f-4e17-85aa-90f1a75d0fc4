% ===================================================================
% 研究方法 (Research Methodology)
% ===================================================================

\section{研究方法}

% ===================================================================
% 专家招聘 (Expert Recruitment)
% ===================================================================
\subsection{专家招聘}

\begin{frame}{专家招聘}

  {\normalsize \textbf{研究背景}}
  \begin{itemize}
    \item 多样性提升群体表现：人类群体内的多样性引入不同观点，提高任务表现
    \item 角色分配的重要性：为智能体指定特定角色可以提高效率
  \end{itemize}

  \vspace{0.3cm}

  \begin{columns}[T]
    \begin{column}{0.48\textwidth}
      {\large \textcolor{red}{\textbf{传统方法的局限}}}
      \vspace{0.2cm}
      \begin{itemize}
        \item \textcolor{red}{\textbf{手动分配}}角色描述
        \item 需要\textcolor{red}{\textbf{先验知识}}和任务理解
        \item \textcolor{red}{\textbf{可扩展性不明确}}
        \item 难以应对多样复杂问题
      \end{itemize}
    \end{column}

    \begin{column}{0.48\textwidth}
      {\large \textcolor{blue}{\textbf{AgentVerse方法}}}
      \vspace{0.2cm}
      \begin{itemize}
        \item \textcolor{blue}{\textbf{自动化}}专家招聘
        \item 招聘者$M_r$\textcolor{blue}{\textbf{动态生成}}专家描述
        \item 根据目标$g \in G$形成专家组$M = M_r(g)$
        \item \textcolor{blue}{\textbf{动态调整}}组成结构
      \end{itemize}
    \end{column}
  \end{columns}

  \vspace{0.3cm}

  \begin{center}
    \fbox{\parbox{0.9\textwidth}{
      \centering
      \textbf{核心优势：}通过评估阶段反馈动态调整多智能体组组成，根据当前状态选择最合适的组合
    }}
  \end{center}

\end{frame}

% ===================================================================
% 协同决策 (Collaborative Decision Making)
% ===================================================================
\subsection{协同决策}

\begin{frame}{\frametitle{协同决策}}
    % 阶段概述
    \centering

    {\normalsize 此阶段旨在让招募的专家智能体们通过有效的通信与协作，共同制定出最终决策。}

    \vspace{0.4cm}

    % 使用 tabularx 环境以更好地控制列宽和文本换行
    \begin{tabularx}{\textwidth}{l|X|X}
        \toprule
        \textbf{特性} & \textbf{水平结构 (Horizontal)} & \textbf{垂直结构 (Vertical)} \\
        \midrule
        \textbf{结构图} &
        % 请确保您的项目中存在 pic/horizontal.pdf 文件
        \parbox[c]{0.4\textwidth}{\includegraphics[width=0.4\linewidth]{pic/horizontal.pdf}} &
        % 请确保您的项目中存在 pic/vertical.pdf 文件
        \parbox[c]{0.4\textwidth}{\includegraphics[width=0.4\linewidth]{pic/vertical.pdf}} \\
        \midrule
        \textbf{核心思想} &
        民主结构，智能体平等协作，共同决策 &
        层级结构，角色分工明确(一个求解器和多个评审者) \\
        \midrule
        \textbf{决策过程} &
        所有智能体$m_i$并行提出各自决策$a_{m_i}$，最后通过一个函数$f$进行整合 &
        求解器$m^*$提出初始决策$a_0^*$，其他评审者提供反馈，求解器根据反馈迭代优化 \\
        \midrule
        \textbf{最终决策} &
        整合所有个体决策：\newline $A = f(\{a_{m_i}\}_i)$ &
        k轮优化后的最终方案：\newline $A = a_k^*$ \\
        \midrule
        \textbf{适用场景} &
        需综合多方意见的任务(如咨询、头脑风暴)&
        需单一优化决策的任务(如数学求解、代码生成) \\
        \bottomrule
    \end{tabularx}
\end{frame}

% ===================================================================
% 动作执行 (Action Execution)
% ===================================================================
\subsection{动作执行}

\begin{frame}
  \frametitle{动作执行}

  {\large \textbf{智能体在环境中执行协同决策阶段确定的群体决策}}

  \vspace{0.5cm}

  \begin{center}
    {\LARGE $s_{\text{old}} \xrightarrow[\text{执行群体决策}]{A} s_{\text{new}} = T(s_{\text{old}}, A)$}
  \end{center}

  \vspace{0.8cm}

  \begin{itemize}
    \item \textbf{输入}：协同决策阶段产生的群体决策 $A$
    \item \textbf{执行}：智能体在环境中执行共同决定的动作
    \item \textbf{转移}：环境状态从 $s_{\text{old}}$ 转移到 $s_{\text{new}} = T(s_{\text{old}}, A)$
  \end{itemize}

\end{frame}

% ===================================================================
% 评估 (Evaluation)
% ===================================================================
\subsection{评估}

\begin{frame}
  \frametitle{评估}

  % TODO: 添加评估的具体内容
  \begin{itemize}
    \item 评估指标
    \item 反馈机制
    \item 性能优化
  \end{itemize}

\end{frame}
