% -------------------------------------------------------------------
% 研究动机 (Research Motivation)
% -------------------------------------------------------------------
\section{研究动机}

\subsection{单一智能体的局限与协作的必要性}
\begin{frame}{单一智能体的局限与协作的必要性}
    % 顶部一行字
    \vspace*{-2em}
    \begin{center}
    	\fontsize{12pt}{14pt}\selectfont \underline{尽管单一自主智能体表现出色，但其能力边界也逐渐显现。}
    \end{center}

    \vspace*{-0.5em}
    % 左右分栏结构
    \begin{columns}[T] % T选项让两栏顶部对齐
    	\begin{column}{0.49\textwidth}
    		% --- 左栏：一个基础的 tcolorbox 样式 ---
    		\begin{tcolorbox}[
    			title={现实世界挑战的复杂性},
    			colback=blue!8,         % 背景颜色
    			colframe=blue!70!black, % 边框颜色
    			fonttitle=\bfseries,    % 标题字体加粗
    			rounded corners         % 设置为圆角
    			]
    			现实世界中的绝大多数复杂问题，例如软件系统开发、跨领域科学研究、或是综合性商业咨询，都需要不同知识背景和技能的专家团队进行紧密协作。这超出了任何一个全能型单一智能体的能力范畴。
    		\end{tcolorbox}
    	\end{column}

    	\begin{column}{0.49\textwidth}
    		% --- 右栏：一个选项更丰富的 tcolorbox 样式 ---
    		\begin{tcolorbox}[
    			enhanced, % 使用增强皮肤，效果更丰富
    			title={现有研究的普遍局限},
    			colback=green!5,            % 内容区域的背景色
    			colframe=green!60!black,    % 边框颜色
    			colbacktitle=green!65!black,% 标题栏的背景色
    			coltitle=white,             % 标题文字的颜色
    			fonttitle=\bfseries,        % 标题字体加粗l
    			boxrule=0.5pt,              % 边框线宽
    			breakable                   % 内容可跨页/帧
    			]
    			\textbf{任务特定，缺乏通用性}: 当前多数多智能体系统的研究高度集中于特定任务（如模拟社交、软件开发），其框架设计和交互模式难以迁移到其他领域。
    			\\[0.5em]
    			\textbf{角色固化，适应性差}: 智能体的角色和能力通常是静态且预先设定的。在面对动态变化的任务需求时，它们无法像人类团队一样灵活地调整角色和策略，适应性严重不足。
    		\end{tcolorbox}
    	\end{column}
    \end{columns}

\end{frame}

\subsection{核心动机与本文目标}
\begin{frame}{核心动机与本文目标}

    如何设计一个通用的多智能体框架，能够有效组织和动态协调多个专家智能体，使其像一个高效的人类团队一样协同工作，最终实现"整体大于部分之和"的系统级智能？
	\vspace{-1em}
    \begin{center}
        \includegraphics[width=0.75\linewidth]{pic/pipeline.pdf}
    \end{center}
    \vspace{-1em}
    基于以上动机，本文提出并实现一个名为AgentVerse的通用多智能体协作框架。该框架通过模拟人类社会中群体解决问题的动态过程（如角色分配、沟通、决策），旨在赋予多智能体系统更强的适应性、通用性和协作效率。
\end{frame}
