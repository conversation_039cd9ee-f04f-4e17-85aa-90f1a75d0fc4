% ===================================================================
% 任务形式化定义 (Task Formal Definition)
% ===================================================================

\section{任务定义}

\begin{frame}{任务定义}

  AGENTVERSE框架中，任务求解过程建模为马尔可夫决策过程（MDP），形式化为元组：
  \[
  \mathcal{M} = (S, A, T, R, G)
  \]

  其中各组件定义如下：
  \begin{itemize}
  	\item \textbf{$S$ (状态空间)}: 全局状态空间，囊括了环境以及所有智能体的内部状态。
  	\item \textbf{$A$ (行动空间)}: 多智能体团队在“协同决策”后形成的联合行动方案的集合。
  	\item \textbf{$T$ (状态转移函数)}: $T: S \times A \rightarrow S$。它描述了系统在状态 $s_{old}$ 执行联合行动 $A$ 后，演进至新状态 $s_{new} = T(s_{old}, A)$的路径。
  	\item \textbf{$R$ (反馈函数)}: $R: S \times G \rightarrow F$。该函数评估当前状态$s_{\text{new}} \in S$与目标$g \in G$的差异，并生成指导性的反馈信息 $r = R(s_{new}, g)$，用于引导后续迭代。
  	\item \textbf{$G$ (目标空间)}: 任务最终期望达成的所有可能结果的集合。
  \end{itemize}

  过程说明：通过迭代执行“专家招募-协同决策-动作执行-评估”四阶段，使状态从初始态逐步逼近目标$g \in G$。
\end{frame}
