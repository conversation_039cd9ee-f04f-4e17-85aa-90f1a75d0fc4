This is XeTeX, Version 3.141592653-2.6-0.999997 (TeX Live 2025) (preloaded format=xelatex 2025.4.2)  28 JUL 2025 21:37
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**main.tex
(./main.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamer.cls
Document Class: beamer 2025/02/04 v3.72 A class for typesetting presentations

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerbasemodes
.sty
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count192
)
\beamer@tempbox=\box52
\beamer@tempcount=\count193
\c@beamerpauses=\count194

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerbasedecod
e.sty
\beamer@slideinframe=\count195
\beamer@minimum=\count196
\beamer@decode@box=\box53
)
\beamer@commentbox=\box54
\beamer@modecount=\count197
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
)
\headdp=\dimen141
\footheight=\dimen142
\sidebarheight=\dimen143
\beamer@tempdim=\dimen144
\beamer@finalheight=\dimen145
\beamer@animht=\dimen146
\beamer@animdp=\dimen147
\beamer@animwd=\dimen148
\beamer@leftmargin=\dimen149
\beamer@rightmargin=\dimen150
\beamer@leftsidebar=\dimen151
\beamer@rightsidebar=\dimen152
\beamer@boxsize=\dimen153
\beamer@vboxoffset=\dimen154
\beamer@descdefault=\dimen155
\beamer@descriptionwidth=\dimen156
\beamer@lastskip=\skip49
\beamer@areabox=\box55
\beamer@animcurrent=\box56
\beamer@animshowbox=\box57
\beamer@sectionbox=\box58
\beamer@logobox=\box59
\beamer@linebox=\box60
\beamer@sectioncount=\count198
\beamer@subsubsectionmax=\count199
\beamer@subsectionmax=\count266
\beamer@sectionmax=\count267
\beamer@totalheads=\count268
\beamer@headcounter=\count269
\beamer@partstartpage=\count270
\beamer@sectionstartpage=\count271
\beamer@subsectionstartpage=\count272
\beamer@animationtempa=\count273
\beamer@animationtempb=\count274
\beamer@xpos=\count275
\beamer@ypos=\count276
\beamer@ypos@offset=\count277
\beamer@showpartnumber=\count278
\beamer@currentsubsection=\count279
\beamer@coveringdepth=\count280
\beamer@sectionadjust=\count281
\beamer@toclastsection=\count282
\beamer@tocsectionnumber=\count283

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerbaseoptio
ns.sty
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
))
\beamer@paperwidth=\skip50
\beamer@paperheight=\skip51

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
)
\Gm@cnth=\count284
\Gm@cntv=\count285
\c@Gm@tempcnt=\count286
\Gm@bindingoffset=\dimen157
\Gm@wd@mp=\dimen158
\Gm@odd@mp=\dimen159
\Gm@even@mp=\dimen160
\Gm@layoutwidth=\dimen161
\Gm@layoutheight=\dimen162
\Gm@layouthoffset=\dimen163
\Gm@layoutvoffset=\dimen164
\Gm@dimlist=\toks18
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/pgf/math/pgfmath.sty
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/pgf/utilities/pgfrcs.s
ty
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/utilities/pgfuti
l-common.tex
\pgfutil@everybye=\toks19
\pgfutil@tempdima=\dimen165
\pgfutil@tempdimb=\dimen166
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/utilities/pgfuti
l-latex.def
\pgfutil@abb=\box61
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/utilities/pgfrcs
.code.tex
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/pgf.revision.tex
)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/pgf/utilities/pgfkeys.
sty
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/utilities/pgfkey
s.code.tex
\pgfkeys@pathtoks=\toks20
\pgfkeys@temptoks=\toks21

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/utilities/pgfkey
slibraryfiltered.code.tex
\pgfkeys@tmptoks=\toks22
)))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/math/pgfmath.cod
e.tex
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/math/pgfmathutil
.code.tex
\pgf@x=\dimen167
\pgf@xa=\dimen168
\pgf@xb=\dimen169
\pgf@xc=\dimen170
\pgf@y=\dimen171
\pgf@ya=\dimen172
\pgf@yb=\dimen173
\pgf@yc=\dimen174
\c@pgf@counta=\count287
\c@pgf@countb=\count288
\c@pgf@countc=\count289
\c@pgf@countd=\count290
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/math/pgfmathpars
er.code.tex
\pgfmath@dimen=\dimen175
\pgfmath@count=\count291
\pgfmath@box=\box62
\pgfmath@toks=\toks23
\pgfmath@stack@operand=\toks24
\pgfmath@stack@operation=\toks25
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/math/pgfmathfunc
tions.code.tex)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/math/pgfmathfunc
tions.basic.code.tex)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/math/pgfmathfunc
tions.trigonometric.code.tex)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/math/pgfmathfunc
tions.random.code.tex)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/math/pgfmathfunc
tions.comparison.code.tex)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/math/pgfmathfunc
tions.base.code.tex)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/math/pgfmathfunc
tions.round.code.tex)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/math/pgfmathfunc
tions.misc.code.tex)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/math/pgfmathfunc
tions.integerarithmetics.code.tex)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/math/pgfmathcalc
.code.tex)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/math/pgfmathfloa
t.code.tex
\c@pgfmathroundto@lastzeros=\count292
)))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/base/size11.clo
File: size11.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/pgf/basiclayer/pgfcore
.sty
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/graphics-cfg/graphics.
cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 106.

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
))
\Gin@req@height=\dimen176
\Gin@req@width=\dimen177
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/pgf/systemlayer/pgfsys
.sty
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/systemlayer/pgfs
ys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)
\pgf@x=\dimen178
\pgf@y=\dimen179
\pgf@xa=\dimen180
\pgf@ya=\dimen181
\pgf@xb=\dimen182
\pgf@yb=\dimen183
\pgf@xc=\dimen184
\pgf@yc=\dimen185
\pgf@xd=\dimen186
\pgf@yd=\dimen187
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count293
\c@pgf@countb=\count294
\c@pgf@countc=\count295
\c@pgf@countd=\count296
\t@pgf@toka=\toks26
\t@pgf@tokb=\toks27
\t@pgf@tokc=\toks28
\pgf@sys@id@count=\count297

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/systemlayer/pgf.
cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-xetex.def

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/systemlayer/pgfs
ys-xetex.def
File: pgfsys-xetex.def 2023-01-15 v3.1.10 (3.1.10)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/systemlayer/pgfs
ys-dvipdfmx.def
File: pgfsys-dvipdfmx.def 2023-01-15 v3.1.10 (3.1.10)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/systemlayer/pgfs
ys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)
\pgfsys@objnum=\count298
)))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/systemlayer/pgfs
yssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count299
\pgfsyssoftpath@bigbuffer@items=\count300
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/systemlayer/pgfs
ysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: xetex.def on input line 274.

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/graphics/mathcolor.ltx
)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/basiclayer/pgfco
re.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/math/pgfint.code
.tex)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/basiclayer/pgfco
repoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen188
\pgf@picmaxx=\dimen189
\pgf@picminy=\dimen190
\pgf@picmaxy=\dimen191
\pgf@pathminx=\dimen192
\pgf@pathmaxx=\dimen193
\pgf@pathminy=\dimen194
\pgf@pathmaxy=\dimen195
\pgf@xx=\dimen196
\pgf@xy=\dimen197
\pgf@yx=\dimen198
\pgf@yy=\dimen199
\pgf@zx=\dimen256
\pgf@zy=\dimen257
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/basiclayer/pgfco
repathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen258
\pgf@path@lasty=\dimen259
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/basiclayer/pgfco
repathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen260
\pgf@shorten@start@additional=\dimen261
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/basiclayer/pgfco
rescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box63
\pgf@hbox=\box64
\pgf@layerbox@main=\box65
\pgf@picture@serial@count=\count301
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/basiclayer/pgfco
regraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen262
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/basiclayer/pgfco
retransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen263
\pgf@pt@y=\dimen264
\pgf@pt@temp=\dimen265
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/basiclayer/pgfco
requick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/basiclayer/pgfco
reobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/basiclayer/pgfco
repathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/basiclayer/pgfco
rearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen266
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/basiclayer/pgfco
reshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen267
\pgf@sys@shading@range@num=\count302
\pgf@shadingcount=\count303
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/basiclayer/pgfco
reimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/basiclayer/pgfco
reexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box66
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/basiclayer/pgfco
relayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/basiclayer/pgfco
retransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/basiclayer/pgfco
repatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/basiclayer/pgfco
rerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/pgf/utilities/xxcolor.
sty
Package: xxcolor 2003/10/24 ver 0.1
\XC@nummixins=\count304
\XC@countmixins=\count305
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
) (d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/hyperref/hyperref.st
y
Package: hyperref 2024-11-05 v7.01l Hypertext links for LaTeX

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.st
y
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/kvdefinekeys/kvdefin
ekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pdfescape/pdfescape.
sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pdftexcmds/pdftexcmd
s.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/infwarerr/infwarerr.
sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/gettitlestring/getti
tlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/kvoptions/kvoptions.st
y
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count306
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/stringenc/stringenc.
sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO
)
)
\@linkdim=\dimen268
\Hy@linkcounter=\count307
\Hy@pagecounter=\count308

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-11-05 v7.01l Hyperref: PDFDocEncoding definition (HO)
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count309
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-11-05 v7.01l Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Option `bookmarks' set `true' on input line 4040.
Package hyperref Info: Option `bookmarksopen' set `true' on input line 4040.
Package hyperref Info: Option `implicit' set `false' on input line 4040.
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode OFF; no redefinition of LaTeX internals.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count310

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen269

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/bigintcalc/bigintcal
c.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count311
\Field@Width=\dimen270
\Fld@charsize=\dimen271
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.
\Hy@abspage=\count312


Package hyperref Message: Stopped early.

)
Package hyperref Info: Driver (autodetected): hxetex.
 (d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/hyperref/hxetex.def
File: hxetex.def 2024-11-05 v7.01l Hyperref driver for XeTeX
\pdfm@box=\box67
\c@Hy@AnnotLevel=\count313
\HyField@AnnotCount=\count314
\Fld@listcount=\count315
\c@bookmark@seq@number=\count316

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/rerunfilecheck/rerunfi
lecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/uniquecounter/unique
counter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
85.
))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerbaserequi
res.sty
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerbasecompa
tibility.sty)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerbasefont.
sty
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\@emptytoks=\toks29
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/sansmathaccent/sansmat
haccent.sty
Package: sansmathaccent 2020/01/31

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/koma-script/scrlfile.s
ty
Package: scrlfile 2024/10/24 v3.43 KOMA-Script package (file load hooks)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/koma-script/scrlfile-h
ook.sty
Package: scrlfile-hook 2024/10/24 v3.43 KOMA-Script package (using LaTeX hooks)


(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/koma-script/scrlogo.st
y
Package: scrlogo 2024/10/24 v3.43 KOMA-Script package (logo)
)))))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerbasetrans
lator.sty
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/translator/translator.
sty
Package: translator 2021-05-31 v1.12d Easy translation of strings in LaTeX
))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerbasemisc.
sty)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerbasetwosc
reens.sty)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerbaseoverl
ay.sty
\beamer@argscount=\count317
\beamer@lastskipcover=\skip52
\beamer@trivlistdepth=\count318
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerbasetitle
.sty)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerbasesecti
on.sty
\c@lecture=\count319
\c@part=\count320
\c@section=\count321
\c@subsection=\count322
\c@subsubsection=\count323
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerbaseframe
.sty
\beamer@framebox=\box68
\beamer@frametitlebox=\box69
\beamer@zoombox=\box70
\beamer@zoomcount=\count324
\beamer@zoomframecount=\count325
\beamer@frametextheight=\dimen272
\c@subsectionslide=\count326
\beamer@frametopskip=\skip53
\beamer@framebottomskip=\skip54
\beamer@frametopskipautobreak=\skip55
\beamer@framebottomskipautobreak=\skip56
\beamer@envbody=\toks30
\framewidth=\dimen273
\c@framenumber=\count327
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerbaseverba
tim.sty
\beamer@verbatimfileout=\write4
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerbaseframe
size.sty
\beamer@splitbox=\box71
\beamer@autobreakcount=\count328
\beamer@autobreaklastheight=\dimen274
\beamer@frametitletoks=\toks31
\beamer@framesubtitletoks=\toks32
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerbaseframe
components.sty
\beamer@footins=\box72
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerbasecolor
.sty)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerbasenotes
.sty
\beamer@frameboxcopy=\box73
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerbasetoc.s
ty)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerbasetempl
ates.sty
\beamer@sbttoks=\toks33

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerbaseauxte
mplates.sty
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerbaseboxes
.sty
\bmb@box=\box74
\bmb@colorbox=\box75
\bmb@boxwidth=\dimen275
\bmb@boxheight=\dimen276
\bmb@prevheight=\dimen277
\bmb@temp=\dimen278
\bmb@dima=\dimen279
\bmb@dimb=\dimen280
\bmb@prevheight=\dimen281
)
\beamer@blockheadheight=\dimen282
))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerbaselocal
structure.sty
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/tools/enumerate.sty
Package: enumerate 2023/07/04 v3.00 enumerate extensions (DPC)
\@enLab=\toks34
)
\beamer@bibiconwidth=\skip57
\c@figure=\count329
\c@table=\count330
\abovecaptionskip=\skip58
\belowcaptionskip=\skip59
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerbasenavig
ation.sty
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerbasenavig
ationsymbols.tex)
\beamer@section@min@dim=\dimen283
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerbasetheor
ems.sty
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip60

For additional information on amsmath, use the `?' option.
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks35
\ex@=\dimen284
))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen285
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count331
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count332
\leftroot@=\count333
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count334
\DOTSCASE@=\count335
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box76
\strutbox@=\box77
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen286
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count336
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count337
\dotsspace@=\muskip18
\c@parentequation=\count338
\dspbrk@lvl=\count339
\tag@help=\toks36
\row@=\count340
\column@=\count341
\maxfields@=\count342
\andhelp@=\toks37
\eqnshift@=\dimen287
\alignsep@=\dimen288
\tagshift@=\dimen289
\tagwidth@=\dimen290
\totwidth@=\dimen291
\lineht@=\dimen292
\@envbody=\toks38
\multlinegap=\skip61
\multlinetaggap=\skip62
\mathdisplay@stack=\toks39
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/amscls/amsthm.sty
Package: amsthm 2020/05/29 v2.20.6
\thm@style=\toks40
\thm@bodyfont=\toks41
\thm@headfont=\toks42
\thm@notefont=\toks43
\thm@headpunct=\toks44
\thm@preskip=\skip63
\thm@postskip=\skip64
\thm@headsep=\skip65
\dth@everypar=\toks45
)
\c@theorem=\count343
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerbasetheme
s.sty))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerthemedefa
ult.sty
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerfonttheme
default.sty)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamercolorthem
edefault.sty)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerinnerthem
edefault.sty
\beamer@dima=\dimen293
\beamer@dimb=\dimen294
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerouterthem
edefault.sty)))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/ctex/ctex.sty
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2025-01-18 L3 programming layer (loader) 

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/l3backend/l3backend-xe
tex.def
File: l3backend-xetex.def 2024-05-08 L3 backend support: XeTeX
\g__graphics_track_int=\count344
\l__pdf_internal_box=\box78
\g__pdf_backend_annotation_int=\count345
\g__pdf_backend_link_int=\count346
))
Package: ctex 2022/07/14 v2.5.10 Chinese adapter in LaTeX (CTEX)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/ctex/ctexhook.sty
Package: ctexhook 2022/07/14 v2.5.10 Document and package hooks (CTEX)
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/ctex/ctexpatch.sty
Package: ctexpatch 2022/07/14 v2.5.10 Patching commands (CTEX)
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/base/fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/base/ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
))
\l__ctex_tmp_int=\count347
\l__ctex_tmp_box=\box79
\l__ctex_tmp_dim=\dimen295
\g__ctex_section_depth_int=\count348
\g__ctex_font_size_int=\count349

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/ctex/config/ctexopts.c
fg
File: ctexopts.cfg 2022/07/14 v2.5.10 Option configuration file (CTEX)
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/ctex/engine/ctex-engin
e-xetex.def
File: ctex-engine-xetex.def 2022/07/14 v2.5.10 XeLaTeX adapter (CTEX)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/xelatex/xecjk/xeCJK.sty
Package: xeCJK 2022/08/05 v3.9.1 Typesetting CJK scripts with XeLaTeX

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/l3packages/xtemplate/x
template.sty
Package: xtemplate 2024-08-16 L3 Experimental prototype document functions
)
\l__xeCJK_tmp_int=\count350
\l__xeCJK_tmp_box=\box80
\l__xeCJK_tmp_dim=\dimen296
\l__xeCJK_tmp_skip=\skip66
\g__xeCJK_space_factor_int=\count351
\l__xeCJK_begin_int=\count352
\l__xeCJK_end_int=\count353
\c__xeCJK_CJK_class_int=\XeTeXcharclass1
\c__xeCJK_FullLeft_class_int=\XeTeXcharclass2
\c__xeCJK_FullRight_class_int=\XeTeXcharclass3
\c__xeCJK_HalfLeft_class_int=\XeTeXcharclass4
\c__xeCJK_HalfRight_class_int=\XeTeXcharclass5
\c__xeCJK_NormalSpace_class_int=\XeTeXcharclass6
\c__xeCJK_CM_class_int=\XeTeXcharclass7
\c__xeCJK_HangulJamo_class_int=\XeTeXcharclass8
\l__xeCJK_last_skip=\skip67
\c__xeCJK_none_node=\count354
\g__xeCJK_node_int=\count355
\c__xeCJK_CJK_node_dim=\dimen297
\c__xeCJK_CJK-space_node_dim=\dimen298
\c__xeCJK_default_node_dim=\dimen299
\c__xeCJK_CJK-widow_node_dim=\dimen300
\c__xeCJK_normalspace_node_dim=\dimen301
\c__xeCJK_default-space_node_skip=\skip68
\l__xeCJK_ccglue_skip=\skip69
\l__xeCJK_ecglue_skip=\skip70
\l__xeCJK_punct_kern_skip=\skip71
\l__xeCJK_indent_box=\box81
\l__xeCJK_last_penalty_int=\count356
\l__xeCJK_last_bound_dim=\dimen302
\l__xeCJK_last_kern_dim=\dimen303
\l__xeCJK_widow_penalty_int=\count357

LaTeX template Info: Declaring template type 'xeCJK/punctuation' taking 0
(template)           argument(s) on line 2396.

\l__xeCJK_fixed_punct_width_dim=\dimen304
\l__xeCJK_mixed_punct_width_dim=\dimen305
\l__xeCJK_middle_punct_width_dim=\dimen306
\l__xeCJK_fixed_margin_width_dim=\dimen307
\l__xeCJK_mixed_margin_width_dim=\dimen308
\l__xeCJK_middle_margin_width_dim=\dimen309
\l__xeCJK_bound_punct_width_dim=\dimen310
\l__xeCJK_bound_margin_width_dim=\dimen311
\l__xeCJK_margin_minimum_dim=\dimen312
\l__xeCJK_kerning_total_width_dim=\dimen313
\l__xeCJK_same_align_margin_dim=\dimen314
\l__xeCJK_different_align_margin_dim=\dimen315
\l__xeCJK_kerning_margin_width_dim=\dimen316
\l__xeCJK_kerning_margin_minimum_dim=\dimen317
\l__xeCJK_bound_dim=\dimen318
\l__xeCJK_reverse_bound_dim=\dimen319
\l__xeCJK_margin_dim=\dimen320
\l__xeCJK_minimum_bound_dim=\dimen321
\l__xeCJK_kerning_margin_dim=\dimen322
\g__xeCJK_family_int=\count358
\l__xeCJK_fam_int=\count359
\g__xeCJK_fam_allocation_int=\count360
\l__xeCJK_verb_case_int=\count361
\l__xeCJK_verb_exspace_skip=\skip72

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/fontspec/fontspec.sty
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/l3packages/xparse/xpar
se.sty
Package: xparse 2024-08-16 L3 Experimental document command parser
)
Package: fontspec 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/fontspec/fontspec-xete
x.sty
Package: fontspec-xetex 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTe
X
\l__fontspec_script_int=\count362
\l__fontspec_language_int=\count363
\l__fontspec_strnum_int=\count364
\l__fontspec_tmp_int=\count365
\l__fontspec_tmpa_int=\count366
\l__fontspec_tmpb_int=\count367
\l__fontspec_tmpc_int=\count368
\l__fontspec_em_int=\count369
\l__fontspec_emdef_int=\count370
\l__fontspec_strong_int=\count371
\l__fontspec_strongdef_int=\count372
\l__fontspec_tmpa_dim=\dimen323
\l__fontspec_tmpb_dim=\dimen324
\l__fontspec_tmpc_dim=\dimen325
 (d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/fontspec/fontspec.cfg)
)) (d:/Scoop/apps/texlive-full/current/texmf-dist/tex/xelatex/xecjk/xeCJK.cfg
File: xeCJK.cfg 2022/08/05 v3.9.1 Configuration file for xeCJK package
))
\ccwd=\dimen326
\l__ctex_ccglue_skip=\skip73
)
\l__ctex_ziju_dim=\dimen327

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/zhnumber/zhnumber.sty
Package: zhnumber 2022/07/14 v3.0 Typesetting numbers with Chinese glyphs
\l__zhnum_scale_int=\count373
\l__zhnum_tmp_int=\count374

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/zhnumber/zhnumber-utf8
.cfg
File: zhnumber-utf8.cfg 2022/07/14 v3.0 Chinese numerals with UTF8 encoding
))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/ctex/scheme/ctex-schem
e-chinese.def
File: ctex-scheme-chinese.def 2022/07/14 v2.5.10 Chinese scheme for generic (CT
EX)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/ctex/config/ctex-name-
utf8.cfg
File: ctex-name-utf8.cfg 2022/07/14 v2.5.10 Caption with encoding UTF-8 (CTEX)
))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/ctex/fontset/ctex-font
set-windows.def
File: ctex-fontset-windows.def 2022/07/14 v2.5.10 Windows fonts definition (CTE
X)

Package fontspec Info: 
(fontspec)             Could not resolve font "KaiTi/B" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: 
(fontspec)             Could not resolve font "SimHei/I" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: 
(fontspec)             Could not resolve font "SimSun/BI" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: 
(fontspec)             Font family 'SimSun(0)' created for font 'SimSun' with
(fontspec)             options
(fontspec)             [Script={CJK},BoldFont={SimHei},ItalicFont={KaiTi}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"SimSun/OT:script=hani;language=dflt;"
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->"SimHei/OT:script=hani;language=dflt;"
(fontspec)             - 'italic' (m/it) with NFSS spec.:
(fontspec)             <->"KaiTi/OT:script=hani;language=dflt;"

))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/ctex/config/ctex.cfg
File: ctex.cfg 2022/07/14 v2.5.10 Configuration file (CTEX)
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
LaTeX Font Info:    Trying to load font information for T1+lmss on input line 1
16.

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/lm/t1lmss.fd
File: t1lmss.fd 2015/05/01 v1.6.1 Font defs for Latin Modern
))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/microtype/microtype.st
y
Package: microtype 2025/02/11 v3.2a Micro-typographical refinements (RS)
\MT@toks=\toks46
\MT@tempbox=\box82
\MT@count=\count375
LaTeX Info: Redefining \noprotrusionifhmode on input line 1087.
LaTeX Info: Redefining \leftprotrusion on input line 1088.
\MT@prot@toks=\toks47
LaTeX Info: Redefining \rightprotrusion on input line 1107.
LaTeX Info: Redefining \textls on input line 1449.
\MT@outer@kern=\dimen328
LaTeX Info: Redefining \microtypecontext on input line 2053.
LaTeX Info: Redefining \textmicrotypecontext on input line 2070.
\MT@listname@count=\count376

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/microtype/microtype-xe
tex.def
File: microtype-xetex.def 2025/02/11 v3.2a Definitions specific to xetex (RS)
LaTeX Info: Redefining \lsstyle on input line 443.
LaTeX Info: Redefining \lslig on input line 451.
\MT@outer@space=\skip74
)
Package microtype Info: Loading configuration file microtype.cfg.

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/microtype/microtype.cf
g
File: microtype.cfg 2025/02/11 v3.2a microtype main configuration file (RS)
)
LaTeX Info: Redefining \microtypesetup on input line 3065.
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/boondox/BOONDOX-cal.st
y
Package: BOONDOX-cal 2017/02/25 v1.02 U/BOONDOX-cal
 (d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/xkeyval/xkeyval.tex
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks48
\XKV@tempa@toks=\toks49
)
\XKV@depth=\count377
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
))
LaTeX Font Info:    Redeclaring math alphabet \mathcal on input line 10.
LaTeX Font Info:    Overwriting math alphabet `\mathcal' in version `bold'
(Font)                  U/BOONDOX-cal/m/n --> U/BOONDOX-cal/b/n on input line 1
1.
)
\sympureletters=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `pureletters' in version `bold'
(Font)                  OT1/mathkerncmss/m/sl --> OT1/mathkerncmss/bx/sl on inp
ut line 20.
 (d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/tools/bm.sty
Package: bm 2023/12/19 v1.2f Bold Symbol Support (DPC/FMi)
\symboldoperators=\mathgroup7
\symboldletters=\mathgroup8
\symboldsymbols=\mathgroup9
Package bm Info: No bold for \OMX/cmex/m/n, using \pmb.
Package bm Info: No bold for \U/msa/m/n, using \pmb.
Package bm Info: No bold for \U/msb/m/n, using \pmb.
\symboldpureletters=\mathgroup10
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 149.
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2024/11/12 v2.9 Span multiple rows of a table
\multirow@colwidth=\skip75
\multirow@cntb=\count378
\multirow@dima=\skip76
\bigstrutjot=\dimen329
) (d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/booktabs/booktabs.st
y
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen330
\lightrulewidth=\dimen331
\cmidrulewidth=\dimen332
\belowrulesep=\dimen333
\belowbottomsep=\dimen334
\aboverulesep=\dimen335
\abovetopsep=\dimen336
\cmidrulesep=\dimen337
\cmidrulekern=\dimen338
\defaultaddspace=\dimen339
\@cmidla=\count379
\@cmidlb=\count380
\@aboverulesep=\dimen340
\@belowrulesep=\dimen341
\@thisruleclass=\count381
\@lastruleclass=\count382
\@thisrulewidth=\dimen342
) (d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/tools/tabularx.sty
Package: tabularx 2023/12/11 v2.12a `tabularx' package (DPC)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/tools/array.sty
Package: array 2024/10/17 v2.6g Tabular extension package (FMi)
\col@sep=\dimen343
\ar@mcellbox=\box83
\extrarowheight=\dimen344
\NC@list=\toks50
\extratabsurround=\skip77
\backup@length=\skip78
\ar@cellbox=\box84
)
\TX@col@width=\dimen345
\TX@old@table=\dimen346
\TX@old@col=\dimen347
\TX@target=\dimen348
\TX@delta=\dimen349
\TX@cols=\count383
\TX@ftn=\toks51
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/pgf/frontendlayer/tikz
.sty
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty
Package: pgf 2023-01-15 v3.1.10 (3.1.10)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/modules/pgfmodul
eshapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box85
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/modules/pgfmodul
eplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/pgf/compatibility/pgfc
omp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen350
\pgf@nodesepend=\dimen351
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/pgf/compatibility/pgfc
omp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/pgf/utilities/pgffor.s
ty
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/utilities/pgffor
.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen352
\pgffor@skip=\dimen353
\pgffor@stack=\toks52
\pgffor@toks=\toks53
))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/frontendlayer/ti
kz/tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/libraries/pgflib
raryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count384
\pgfplotmarksize=\dimen354
)
\tikz@lastx=\dimen355
\tikz@lasty=\dimen356
\tikz@lastxsaved=\dimen357
\tikz@lastysaved=\dimen358
\tikz@lastmovetox=\dimen359
\tikz@lastmovetoy=\dimen360
\tikzleveldistance=\dimen361
\tikzsiblingdistance=\dimen362
\tikz@figbox=\box86
\tikz@figbox@bg=\box87
\tikz@tempbox=\box88
\tikz@tempbox@bg=\box89
\tikztreelevel=\count385
\tikznumberofchildren=\count386
\tikznumberofcurrentchild=\count387
\tikz@fig@count=\count388

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/modules/pgfmodul
ematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count389
\pgfmatrixcurrentcolumn=\count390
\pgf@matrix@numberofcolumns=\count391
)
\tikz@expandcount=\count392

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/frontendlayer/ti
kz/libraries/tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/frontendlayer/ti
kz/libraries/tikzlibraryshapes.code.tex
File: tikzlibraryshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/frontendlayer/ti
kz/libraries/tikzlibraryshapes.geometric.code.tex
File: tikzlibraryshapes.geometric.code.tex 2023-01-15 v3.1.10 (3.1.10)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/libraries/shapes
/pgflibraryshapes.geometric.code.tex
File: pgflibraryshapes.geometric.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/frontendlayer/ti
kz/libraries/tikzlibraryshapes.misc.code.tex
File: tikzlibraryshapes.misc.code.tex 2023-01-15 v3.1.10 (3.1.10)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/libraries/shapes
/pgflibraryshapes.misc.code.tex
File: pgflibraryshapes.misc.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/frontendlayer/ti
kz/libraries/tikzlibraryshapes.symbols.code.tex
File: tikzlibraryshapes.symbols.code.tex 2023-01-15 v3.1.10 (3.1.10)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/libraries/shapes
/pgflibraryshapes.symbols.code.tex
File: pgflibraryshapes.symbols.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/frontendlayer/ti
kz/libraries/tikzlibraryshapes.arrows.code.tex
File: tikzlibraryshapes.arrows.code.tex 2023-01-15 v3.1.10 (3.1.10)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/libraries/shapes
/pgflibraryshapes.arrows.code.tex
File: pgflibraryshapes.arrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/frontendlayer/ti
kz/libraries/tikzlibraryshapes.callouts.code.tex
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/libraries/shapes
/pgflibraryshapes.callouts.code.tex))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/frontendlayer/ti
kz/libraries/tikzlibraryshapes.multipart.code.tex
File: tikzlibraryshapes.multipart.code.tex 2023-01-15 v3.1.10 (3.1.10)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/libraries/shapes
/pgflibraryshapes.multipart.code.tex
File: pgflibraryshapes.multipart.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodepartlowerbox=\box90
\pgfnodeparttwobox=\box91
\pgfnodepartthreebox=\box92
\pgfnodepartfourbox=\box93
\pgfnodeparttwentybox=\box94
\pgfnodepartnineteenbox=\box95
\pgfnodeparteighteenbox=\box96
\pgfnodepartseventeenbox=\box97
\pgfnodepartsixteenbox=\box98
\pgfnodepartfifteenbox=\box99
\pgfnodepartfourteenbox=\box100
\pgfnodepartthirteenbox=\box101
\pgfnodeparttwelvebox=\box102
\pgfnodepartelevenbox=\box103
\pgfnodeparttenbox=\box104
\pgfnodepartninebox=\box105
\pgfnodeparteightbox=\box106
\pgfnodepartsevenbox=\box107
\pgfnodepartsixbox=\box108
\pgfnodepartfivebox=\box109
)))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/frontendlayer/ti
kz/libraries/tikzlibraryarrows.code.tex
File: tikzlibraryarrows.code.tex 2023-01-15 v3.1.10 (3.1.10)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/libraries/pgflib
raryarrows.code.tex
File: pgflibraryarrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\arrowsize=\dimen363
))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/frontendlayer/ti
kz/libraries/tikzlibrarypositioning.code.tex
File: tikzlibrarypositioning.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/frontendlayer/ti
kz/libraries/tikzlibrarycalc.code.tex
File: tikzlibrarycalc.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/pgfgantt/pgfgantt.sty
Package: pgfgantt 2024/06/19 v5.0a Draw Gantt diagrams with TikZ

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/frontendlayer/ti
kz/libraries/tikzlibrarybackgrounds.code.tex
File: tikzlibrarybackgrounds.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@layerbox@background=\box110
\pgf@layerboxsaved@background=\box111
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/frontendlayer/ti
kz/libraries/tikzlibrarypatterns.code.tex
File: tikzlibrarypatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/libraries/pgflib
rarypatterns.code.tex
File: pgflibrarypatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/pgf/utilities/pgfcalen
dar.sty
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pgf/utilities/pgfcal
endar.code.tex
File: pgfcalendar.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfcalendarcurrentjulian=\count393
\pgf@cal@easter@Y=\count394
\pgf@cal@easter@G=\count395
\pgf@cal@easter@C=\count396
\pgf@cal@easter@X=\count397
\pgf@cal@easter@Z=\count398
\pgf@cal@easter@D=\count399
\pgf@cal@easter@E=\count400
\pgf@cal@easter@N=\count401
\pgf@cal@easter@M=\count402
\pgf@cal@easter@julianday=\count403
))
\gtt@currentline=\count404
\gtt@lasttitleline=\count405
\gtt@currgrid=\count406
\gtt@chartwidth=\count407
\gtt@lasttitleslot=\count408
\gtt@elementid=\count409
\gtt@today@slot=\count410
\gtt@startjulian=\count411
\gtt@endjulian=\count412
\gtt@chartid=\count413
\gtt@vrule@slot=\count414
\gtt@calendar@slots=\count415
\gtt@calendar@weeknumber=\count416
\gtt@calendar@startofweek=\count417
\gtt@left@slot=\count418
\gtt@right@slot=\count419
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/soul/soul.sty
Package: soul 2023-06-14 v3.1 Permit use of UTF-8 characters in soul (HO)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/soul/soul-ori.sty
Package: soul-ori 2023-06-14 v3.1 letterspacing/underlining (mf)
\SOUL@word=\toks54
\SOUL@lasttoken=\toks55
\SOUL@syllable=\toks56
\SOUL@cmds=\toks57
\SOUL@buffer=\toks58
\SOUL@token=\toks59
\SOUL@syllgoal=\dimen364
\SOUL@syllwidth=\dimen365
\SOUL@charkern=\dimen366
\SOUL@hyphkern=\dimen367
\SOUL@dimen=\dimen368
\SOUL@dimeni=\dimen369
\SOUL@minus=\count420
\SOUL@comma=\count421
\SOUL@apo=\count422
\SOUL@grave=\count423
\SOUL@spaceskip=\skip79
\SOUL@ttwidth=\dimen370
\SOUL@uldp=\dimen371
\SOUL@ulht=\dimen372
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/etexcmds/etexcmds.st
y
Package: etexcmds 2019/12/15 v1.7 Avoid name clashes with e-TeX commands (HO)
)) (d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/base/latexsym.sty
Package: latexsym 1998/08/17 v2.2e Standard LaTeX package (lasy symbols)
\symlasy=\mathgroup11
LaTeX Font Info:    Overwriting symbol font `lasy' in version `bold'
(Font)                  U/lasy/m/n --> U/lasy/b/n on input line 52.
) (d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/tools/multicol.sty
Package: multicol 2024/09/14 v1.9i multicolumn formatting (FMi)
\c@tracingmulticols=\count424
\mult@box=\box112
\multicol@leftmargin=\dimen373
\c@unbalance=\count425
\c@collectmore=\count426
\doublecol@number=\count427
\multicoltolerance=\count428
\multicolpretolerance=\count429
\full@width=\dimen374
\page@free=\dimen375
\premulticols=\dimen376
\postmulticols=\dimen377
\multicolsep=\skip80
\multicolbaselineskip=\skip81
\partial@page=\box113
\last@line=\box114
\mc@boxedresult=\box115
\maxbalancingoverflow=\dimen378
\mult@rightbox=\box116
\mult@grightbox=\box117
\mult@firstbox=\box118
\mult@gfirstbox=\box119
\@tempa=\box120
\@tempa=\box121
\@tempa=\box122
\@tempa=\box123
\@tempa=\box124
\@tempa=\box125
\@tempa=\box126
\@tempa=\box127
\@tempa=\box128
\@tempa=\box129
\@tempa=\box130
\@tempa=\box131
\@tempa=\box132
\@tempa=\box133
\@tempa=\box134
\@tempa=\box135
\@tempa=\box136
\@tempa=\box137
\@tempa=\box138
\@tempa=\box139
\@tempa=\box140
\@tempa=\box141
\@tempa=\box142
\@tempa=\box143
\@tempa=\box144
\@tempa=\box145
\@tempa=\box146
\@tempa=\box147
\@tempa=\box148
\@tempa=\box149
\@tempa=\box150
\@tempa=\box151
\@tempa=\box152
\@tempa=\box153
\@tempa=\box154
\@tempa=\box155
\c@minrows=\count430
\c@columnbadness=\count431
\c@finalcolumnbadness=\count432
\last@try=\dimen379
\multicolovershoot=\dimen380
\multicolundershoot=\dimen381
\mult@nat@firstbox=\box156
\colbreak@box=\box157
\mc@col@check@num=\count433
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/fundus-calligra/callig
ra.sty
Package: calligra 2012/04/10 v1.9 LaTeX package calligra
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/stackengine/stackengin
e.sty
Package: stackengine 2021/07/22 v4.11\ Stacking text and objects in convenient 
ways

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/listofitems/listofit
ems.sty
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/listofitems/listofit
ems.tex
\loi_cnt_foreach_nest=\count434
\loi_nestcnt=\count435
)
Package: listofitems 2024/03/09 v1.65 Grab items in lists using user-specified 
sep char (CT)
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/tools/calc.sty
Package: calc 2023/07/08 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count436
\calc@Bcount=\count437
\calc@Adimen=\dimen382
\calc@Bdimen=\dimen383
\calc@Askip=\skip82
\calc@Bskip=\skip83
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count438
\calc@Cskip=\skip84
)
\c@@stackindex=\count439
\@boxshift=\skip85
\stack@tmplength=\skip86
\temp@stkl=\skip87
\@stackedboxwidth=\skip88
\@addedbox=\box158
\@anchorbox=\box159
\@insetbox=\box160
\se@backgroundbox=\box161
\stackedbox=\box162
\@centerbox=\box163
\c@ROWcellindex@=\count440
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/pstricks/pstricks.sty
Package: pstricks 2024/02/02 v0.75 LaTeX wrapper for `PSTricks' (RN,HV)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/xkeyval/pst-xkey.tex
File: pst-xkey.tex 2005/11/25 v1.6 PSTricks specialization of xkeyval (HA)
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pstricks/pstricks.te
x
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pstricks/pst-fp.tex
`pst-fp' v0.06, 2020/11/20 (hv)
\pstFP@xs=\count441
\pstFP@xia=\count442
\pstFP@xib=\count443
\pstFP@xfa=\count444
\pstFP@xfb=\count445
\pstFP@rega=\count446
\pstFP@regb=\count447
\pstFP@regs=\count448
\pstFP@times=\count449
)
\psLoopIndex=\count450

`PSTricks' v3.21  <2024/11/16> (tvz,hv)
\pst@dima=\dimen384
\pst@dimb=\dimen385
\pst@dimc=\dimen386
\pst@dimd=\dimen387
\pst@dimg=\dimen388
\pst@dimh=\dimen389
\pst@dimm=\dimen390
\pst@dimn=\dimen391
\pst@dimo=\dimen392
\pst@dimp=\dimen393
\pst@hbox=\box164
\pst@ibox=\box165
\pst@boxg=\box166
\pst@cnta=\count451
\pst@cntb=\count452
\pst@cntc=\count453
\pst@cntd=\count454
\pst@cntg=\count455
\pst@cnth=\count456
\pst@cntm=\count457
\pst@cntn=\count458
\pst@cnto=\count459
\pst@cntp=\count460
\@zero=\count461
\pst@toks=\toks60
--- We are running latex or xelatex ---

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/xelatex/xetex-pstricks/pstri
cks.con
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pstricks/config/xdvi
pdfmx.cfg))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pstricks/pstricks-co
lor.tex)
\psunit=\dimen394
\psxunit=\dimen395
\psyunit=\dimen396
\pst@C@@rType=\count462
\pslinewidth=\dimen397
\psk@startLW=\dimen398
\psk@endLW=\dimen399

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pstricks/pstricks-ar
rows.tex
\pshooklength=\dimen400
\pshookwidth=\dimen401
)
\pst@customdefs=\toks61

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pstricks/pstricks-do
ts.tex)
\pslinearc=\dimen402
\pst@symbolStep=\dimen403
\pst@symbolWidth=\dimen404
\pst@symbolLinewidth=\dimen405
\everypsbox=\toks62
\psframesep=\dimen406
\pslabelsep=\dimen407
\sh@wgridXunit=\dimen408
\sh@wgridYunit=\dimen409
\pst@shift=\dimen410
\ps@imagectr=\count463

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/xelatex/xetex-pstricks/pstri
cks.con
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pstricks/config/xdvi
pdfmx.cfg
Using PSTricks configuration for LaTeX+dvipdfmx
)))
File: pstricks.tex 2024/11/16 v3.21 `PSTricks' (tvz,hv)

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pstricks/pst-fp.tex
`pst-fp' v0.06, 2020/11/20 (hv))
File: pst-fp.tex 2020/11/20 v0.06 `PST-fp' (hv)

>>> Loading XeTeX special macros

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/generic/pstricks/pstricks-xe
tex.def))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/tcolorbox/tcolorbox.st
y
Package: tcolorbox 2024/10/22 version 6.4.1 text color boxes
 (d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/tools/verbatim.sty
Package: verbatim 2024-01-22 v1.5x LaTeX2e package for verbatim enhancements
\every@verbatim=\toks63
\verbatim@line=\toks64
\verbatim@in@stream=\read3
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/environ/environ.sty
Package: environ 2014/05/04 v0.3 A new way to define environments

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/trimspaces/trimspaces.
sty
Package: trimspaces 2009/09/17 v1.1 Trim spaces around a token list
))
\tcb@titlebox=\box167
\tcb@upperbox=\box168
\tcb@lowerbox=\box169
\tcb@phantombox=\box170
\c@tcbbreakpart=\count464
\c@tcblayer=\count465
\c@tcolorbox@number=\count466
\l__tcobox_tmpa_box=\box171
\l__tcobox_tmpa_dim=\dimen411
\tcb@temp=\box172
\tcb@temp=\box173
\tcb@temp=\box174
\tcb@temp=\box175
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/tcolorbox/tcbskins.cod
e.tex
Library (tcolorbox): 'tcbskins.code.tex' version '6.4.1'

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/tikzfill/tikzfill.imag
e.sty
Package: tikzfill.image 2023/08/08 v1.0.1 Image filling library for TikZ

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/tikzfill/tikzfill-comm
on.sty
Package: tikzfill-common 2023/08/08 v1.0.1 Auxiliary code for tikzfill
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/tikzfill/tikzlibraryfi
ll.image.code.tex
File: tikzlibraryfill.image.code.tex 2023/08/08 v1.0.1 Image filling library
\l__tikzfill_img_box=\box176
))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/tcolorbox/tcbskinsjigs
aw.code.tex
Library (tcolorbox): 'tcbskinsjigsaw.code.tex' version '6.4.1'
))
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/tcolorbox/tcbbreakable
.code.tex
Library (tcolorbox): 'tcbbreakable.code.tex' version '6.4.1'
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/pdfcol/pdfcol.sty
Package: pdfcol 2022-09-21 v1.7 Handle new color stacks for pdfTeX (HO)
Package pdfcol Info: Interface disabled because of missing PDF mode of pdfTeX.
)
Package pdfcol Info: pdfTeX's color stacks are not available.
\tcb@testbox=\box177
\tcb@totalupperbox=\box178
\tcb@totallowerbox=\box179
)
(./theme.sty
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerouterthem
esmoothbars.sty)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/beamer/beamerinnerthem
ecircles.sty))
\c@thm=\count467


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                \hskip 10.95pt
(hyperref)                replaced by space on input line 98.


Package fontspec Info: 
(fontspec)             Could not resolve font "Microsoft YaHei Bold/I" (it
(fontspec)             probably doesn't exist).


Package fontspec Info: 
(fontspec)             Could not resolve font "Microsoft YaHei/I" (it probably
(fontspec)             doesn't exist).


Package fontspec Info: 
(fontspec)             Font family 'MicrosoftYaHei(0)' created for font
(fontspec)             'Microsoft YaHei' with options
(fontspec)             [Script={CJK},BoldFont={* Bold}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.: <->"Microsoft
(fontspec)             YaHei/OT:script=hani;language=dflt;"
(fontspec)             - 'bold' (b/n) with NFSS spec.: <->"Microsoft YaHei
(fontspec)             Bold/OT:script=hani;language=dflt;"
(fontspec)             - 'bold italic' (b/it) with NFSS spec.: <->"Microsoft
(fontspec)             YaHei/BI/OT:script=hani;language=dflt;"

(build/main.aux)
\openout1 = `main.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 98.
LaTeX Font Info:    ... okay on input line 98.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 98.
LaTeX Font Info:    ... okay on input line 98.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 98.
LaTeX Font Info:    ... okay on input line 98.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 98.
LaTeX Font Info:    ... okay on input line 98.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 98.
LaTeX Font Info:    ... okay on input line 98.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 98.
LaTeX Font Info:    ... okay on input line 98.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 98.
LaTeX Font Info:    ... okay on input line 98.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 98.
LaTeX Font Info:    ... okay on input line 98.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 98.
LaTeX Font Info:    ... okay on input line 98.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 98.
LaTeX Font Info:    ... okay on input line 98.

*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: custom
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: includehead includefoot 
* h-part:(L,W,R)=(28.45274pt, 307.28987pt, 28.45274pt)
* v-part:(T,H,B)=(0.0pt, 273.14662pt, 0.0pt)
* \paperwidth=364.19536pt
* \paperheight=273.14662pt
* \textwidth=307.28987pt
* \textheight=244.6939pt
* \oddsidemargin=-43.81725pt
* \evensidemargin=-43.81725pt
* \topmargin=-72.26999pt
* \headheight=14.22636pt
* \headsep=0.0pt
* \topskip=11.0pt
* \footskip=14.22636pt
* \marginparwidth=4.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

Package hyperref Info: Link coloring OFF on input line 98.
(build/main.out) (build/main.out)
\@outlinefile=\write5
\openout5 = `main.out'.

LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/cmss/m/n on input line 98.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/cmss/b/n on input line 98.
\symnumbers=\mathgroup12
LaTeX Font Info:    Redeclaring symbol font `pureletters' on input line 98.
LaTeX Font Info:    Encoding `OT1' has changed to `T1' for symbol font
(Font)              `pureletters' in the math version `normal' on input line 98
.
LaTeX Font Info:    Overwriting symbol font `pureletters' in version `normal'
(Font)                  OT1/mathkerncmss/m/sl --> T1/lmss/m/it on input line 98
.
LaTeX Font Info:    Encoding `OT1' has changed to `T1' for symbol font
(Font)              `pureletters' in the math version `bold' on input line 98.
LaTeX Font Info:    Overwriting symbol font `pureletters' in version `bold'
(Font)                  OT1/mathkerncmss/bx/sl --> T1/lmss/m/it on input line 9
8.
LaTeX Font Info:    Overwriting math alphabet `\mathrm' in version `normal'
(Font)                  OT1/cmss/m/n --> T1/lmr/m/n on input line 98.
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 98.
LaTeX Font Info:    Redeclaring math alphabet \mathsf on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> T1/lmss/m/n on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> T1/lmss/m/n on input line 98.
LaTeX Font Info:    Redeclaring math alphabet \mathit on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> T1/lmss/m/it on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> T1/lmss/m/it on input line 98.
LaTeX Font Info:    Redeclaring math alphabet \mathtt on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> T1/lmtt/m/n on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> T1/lmtt/m/n on input line 98.
LaTeX Font Info:    Overwriting symbol font `numbers' in version `bold'
(Font)                  T1/lmss/m/n --> T1/lmss/b/n on input line 98.
LaTeX Font Info:    Overwriting symbol font `pureletters' in version `bold'
(Font)                  T1/lmss/m/it --> T1/lmss/b/it on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathrm' in version `bold'
(Font)                  OT1/cmss/b/n --> T1/lmr/b/n on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  T1/lmss/b/n --> T1/lmss/b/n on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  T1/lmss/m/n --> T1/lmss/b/n on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  T1/lmss/m/it --> T1/lmss/b/it on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  T1/lmtt/m/n --> T1/lmtt/b/n on input line 98.
LaTeX Font Info:    Redeclaring symbol font `boldpureletters' on input line 98.

LaTeX Font Info:    Encoding `OT1' has changed to `T1' for symbol font
(Font)              `boldpureletters' in the math version `normal' on input lin
e 98.
LaTeX Font Info:    Overwriting symbol font `boldpureletters' in version `norma
l'
(Font)                  OT1/mathkerncmss/bx/sl --> T1/lmss/bx/it on input line 
98.
LaTeX Font Info:    Encoding `OT1' has changed to `T1' for symbol font
(Font)              `boldpureletters' in the math version `bold' on input line 
98.
LaTeX Font Info:    Overwriting symbol font `boldpureletters' in version `bold'

(Font)                  OT1/mathkerncmss/bx/sl --> T1/lmss/bx/it on input line 
98.

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/translator/translator-
basic-dictionary-English.dict
Dictionary: translator-basic-dictionary, Language: English 
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/translator/translator-
bibliography-dictionary-English.dict
Dictionary: translator-bibliography-dictionary, Language: English 
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/translator/translator-
environment-dictionary-English.dict
Dictionary: translator-environment-dictionary, Language: English 
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/translator/translator-
months-dictionary-English.dict
Dictionary: translator-months-dictionary, Language: English 
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/translator/translator-
numbers-dictionary-English.dict
Dictionary: translator-numbers-dictionary, Language: English 
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/translator/translator-
theorem-dictionary-English.dict
Dictionary: translator-theorem-dictionary, Language: English 
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/ctex/dictionary/transl
ator-theorem-dictionary-ChineseUTF8.dict
Dictionary: translator-theorem-dictionary, Language: ChineseUTF8 2022/07/14 v2.
5.10 Chinese translation for theorem name (CTEX)
)

Package fontspec Info: 
(fontspec)             Adjusting the maths setup (use [no-math] to avoid
(fontspec)             this).

\symlegacymaths=\mathgroup13
LaTeX Font Info:    Overwriting symbol font `legacymaths' in version `bold'
(Font)                  OT1/cmr/m/n --> OT1/cmr/bx/n on input line 98.
LaTeX Font Info:    Redeclaring math accent \acute on input line 98.
LaTeX Font Info:    Redeclaring math accent \grave on input line 98.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 98.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 98.
LaTeX Font Info:    Redeclaring math accent \bar on input line 98.
LaTeX Font Info:    Redeclaring math accent \breve on input line 98.
LaTeX Font Info:    Redeclaring math accent \check on input line 98.
LaTeX Font Info:    Redeclaring math accent \hat on input line 98.
LaTeX Font Info:    Redeclaring math accent \dot on input line 98.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 98.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 98.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 98.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 98.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 98.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 98.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 98.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 98.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 98.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 98.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 98.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 98.
LaTeX Font Info:    Redeclaring math symbol \mathdollar on input line 98.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 98.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 98.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/lmr/m/n on input line 98.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 98.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmss/b/n --> TU/lmr/m/n on input line 98.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  TU/lmr/m/n --> TU/lmr/m/n on input line 98.
LaTeX Font Info:    Redeclaring math alphabet \mathrm on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  T1/lmss/m/it --> TU/lmr/m/it on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  T1/lmss/b/n --> TU/lmr/b/n on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  T1/lmss/m/n --> TU/lmss/m/n on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  T1/lmtt/m/n --> TU/lmtt/m/n on input line 98.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  TU/lmr/m/n --> TU/lmr/b/n on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  T1/lmss/b/it --> TU/lmr/b/it on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  T1/lmss/b/n --> TU/lmss/b/n on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  T1/lmtt/b/n --> TU/lmtt/b/n on input line 98.
LaTeX Info: Redefining \microtypecontext on input line 98.
Package microtype Info: Applying patch `item' on input line 98.
Package microtype Info: Applying patch `toc' on input line 98.
Package microtype Info: Applying patch `eqnum' on input line 98.
Package microtype Info: Applying patch `footnote' on input line 98.
Package microtype Info: Applying patch `verbatim' on input line 98.
LaTeX Info: Redefining \microtypesetup on input line 98.
Package microtype Info: Character protrusion enabled (level 2).
Package microtype Info: Using default protrusion set `alltext'.
Package microtype Info: No adjustment of tracking.
Package microtype Info: No adjustment of spacing.
Package microtype Info: No adjustment of kerning.
Package microtype Info: Loading generic protrusion settings for font family
(microtype)             `lmss' (encoding: T1).
(microtype)             For optimal results, create family-specific settings.
(microtype)             See the microtype manual for details.
 (build/main.nav)

Package fontspec Info: 
(fontspec)             Could not resolve font "KaiTi/BI" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: 
(fontspec)             Could not resolve font "KaiTi/B" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: 
(fontspec)             Could not resolve font "KaiTi/I" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: 
(fontspec)             Font family 'KaiTi(0)' created for font 'KaiTi' with
(fontspec)             options [Script={CJK}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"KaiTi/OT:script=hani;language=dflt;"



File: logo.jpg Graphic file (type bmp)
<logo.jpg>
[1

] (build/main.toc)

File: logo.jpg Graphic file (type bmp)
<logo.jpg>
[2

] (./sections/background.tex (build/main.toc)

File: logo.jpg Graphic file (type bmp)
<logo.jpg>
[3

]

File: logo.jpg Graphic file (type bmp)
<logo.jpg>
[4

]) (./sections/motivation.tex (build/main.toc)

File: logo.jpg Graphic file (type bmp)
<logo.jpg>
[5

] (build/main.toc)

File: logo.jpg Graphic file (type bmp)
<logo.jpg>
[6

]
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/microtype/mt-LatinMode
rnRoman.cfg
File: mt-LatinModernRoman.cfg 2021/02/21 v1.1 microtype config. file: Latin Mod
ern Roman (RS)
)
LaTeX Font Info:    Trying to load font information for U+msa on input line 50.


(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/microtype/mt-msa.cfg
File: mt-msa.cfg 2006/02/04 v1.1 microtype config. file: AMS symbols (a) (RS)
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 50.


(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/microtype/mt-msb.cfg
File: mt-msb.cfg 2005/06/01 v1.0 microtype config. file: AMS symbols (b) (RS)
)
LaTeX Font Info:    Font shape `T1/lmss/m/it' in size <12> not available
(Font)              Font shape `T1/lmss/m/sl' tried instead on input line 50.
LaTeX Font Info:    Font shape `T1/lmss/m/it' in size <8> not available
(Font)              Font shape `T1/lmss/m/sl' tried instead on input line 50.
LaTeX Font Info:    Font shape `T1/lmss/m/it' in size <6> not available
(Font)              Font shape `T1/lmss/m/sl' tried instead on input line 50.

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/microtype/mt-cmr.cfg
File: mt-cmr.cfg 2013/05/19 v2.2 microtype config. file: Computer Modern Roman 
(RS)
)
LaTeX Font Info:    Font shape `T1/lmss/bx/it' in size <12> not available
(Font)              Font shape `T1/lmss/bx/sl' tried instead on input line 50.
LaTeX Font Info:    Font shape `T1/lmss/bx/it' in size <8> not available
(Font)              Font shape `T1/lmss/bx/sl' tried instead on input line 50.
LaTeX Font Info:    Font shape `T1/lmss/bx/it' in size <6> not available
(Font)              Font shape `T1/lmss/bx/sl' tried instead on input line 50.

Overfull \hbox (4.71013pt too wide) in paragraph at lines 50--50
 []$ 
 []


LaTeX Font Warning: Font shape `TU/KaiTi(0)/b/n' undefined
(Font)              using `TU/KaiTi(0)/m/n' instead on input line 50.


Underfull \hbox (badness 1348) in paragraph at lines 50--50
\TU/KaiTi(0)/m/n/9.03374 能 的 专 家 团 队 进 行 紧 密 协 作。|
 []



File: logo.jpg Graphic file (type bmp)
<logo.jpg>
[7

] (build/main.toc)

File: logo.jpg Graphic file (type bmp)
<logo.jpg>
[8

]
File: pic/pipeline.pdf Graphic file (type pdf)
<use pic/pipeline.pdf>


File: logo.jpg Graphic file (type bmp)
<logo.jpg>
[9

]) (./sections/task_definition.tex (build/main.toc)

File: logo.jpg Graphic file (type bmp)
<logo.jpg>
[10

]
LaTeX Font Info:    Font shape `T1/lmss/m/it' in size <9.03374> not available
(Font)              Font shape `T1/lmss/m/sl' tried instead on input line 24.
LaTeX Font Info:    Font shape `T1/lmss/m/it' in size <5> not available
(Font)              Font shape `T1/lmss/m/sl' tried instead on input line 24.
LaTeX Font Info:    Font shape `T1/lmss/bx/it' in size <9.03374> not available
(Font)              Font shape `T1/lmss/bx/sl' tried instead on input line 24.
LaTeX Font Info:    Font shape `T1/lmss/bx/it' in size <5> not available
(Font)              Font shape `T1/lmss/bx/sl' tried instead on input line 24.
LaTeX Font Info:    Trying to load font information for U+BOONDOX-cal on input 
line 24.

(d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/boondox/uboondox-cal.f
d
File: uboondox-cal.fd 2011/04/26 Fontinst v1.933 font definitions for U/BOONDOX
-cal.
)
LaTeX Font Info:    Font shape `U/BOONDOX-cal/m/n' will be
(Font)              scaled to size 9.03374pt on input line 24.
LaTeX Font Info:    Font shape `U/BOONDOX-cal/m/n' will be
(Font)              scaled to size 6.0pt on input line 24.
LaTeX Font Info:    Font shape `U/BOONDOX-cal/m/n' will be
(Font)              scaled to size 5.0pt on input line 24.


File: logo.jpg Graphic file (type bmp)
<logo.jpg>
[11

]) (./sections/methodology.tex (build/main.toc)

File: logo.jpg Graphic file (type bmp)
<logo.jpg>
[12

] (build/main.toc)

File: logo.jpg Graphic file (type bmp)
<logo.jpg>
[13

]

File: logo.jpg Graphic file (type bmp)
<logo.jpg>
[14

] (build/main.toc)

File: logo.jpg Graphic file (type bmp)
<logo.jpg>
[15

]
File: pic/horizontal.pdf Graphic file (type pdf)
<use pic/horizontal.pdf>

Overfull \hbox (5.7366pt too wide) in paragraph at lines 98--98
[]|$[]$| 
 []

File: pic/vertical.pdf Graphic file (type pdf)
<use pic/vertical.pdf>

Overfull \hbox (5.7366pt too wide) in paragraph at lines 98--98
[]|$[]$| 
 []



File: logo.jpg Graphic file (type bmp)
<logo.jpg>
[16

] (build/main.toc)

File: logo.jpg Graphic file (type bmp)
<logo.jpg>
[17

]
Overfull \hbox (9.8908pt too wide) in paragraph at lines 163--163
 [] 
 []


Overfull \vbox (25.60745pt too high) detected at line 163
 []



File: logo.jpg Graphic file (type bmp)
<logo.jpg>
[18

] (build/main.toc)

File: logo.jpg Graphic file (type bmp)
<logo.jpg>
[19

]

File: logo.jpg Graphic file (type bmp)
<logo.jpg>
[20

])
LaTeX Font Info:    Font shape `T1/lmss/m/it' in size <24.88> not available
(Font)              Font shape `T1/lmss/m/sl' tried instead on input line 144.
LaTeX Font Info:    Trying to load font information for T1+lmr on input line 14
4.
 (d:/Scoop/apps/texlive-full/current/texmf-dist/tex/latex/lm/t1lmr.fd
File: t1lmr.fd 2015/05/01 v1.6.1 Font defs for Latin Modern
)

File: logo.jpg Graphic file (type bmp)
<logo.jpg>
[21

]
\tf@nav=\write6
\openout6 = `main.nav'.

\tf@toc=\write7
\openout7 = `main.toc'.

\tf@snm=\write8
\openout8 = `main.snm'.

 (build/main.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2022/07/14>
 ***********


LaTeX Font Warning: Some font shapes were not available, defaults substituted.

Package rerunfilecheck Info: File `main.out' has not changed.
(rerunfilecheck)             Checksum: 79A12E5538DE3069ADD3505441C35F91;951.
 ) 
Here is how much of TeX's memory you used:
 42060 strings out of 473832
 963396 string characters out of 5725815
 1480351 words of memory out of 5000000
 64198 multiletter control sequences out of 15000+600000
 645421 words of font info for 147 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 128i,20n,123p,1001b,1369s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on build/main.xdv (21 pages, 646644 bytes).
